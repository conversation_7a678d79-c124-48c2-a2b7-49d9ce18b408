package jkd_import

import (
	"QuestionsDownloader/pkg/utils"
	"QuestionsDownloader/types"
	"bytes"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"strings"
)

const (
	baseURL              = "http://localhost:8081/mgt/question/import-create-set"
	importFilePath       = "excel_0629"
	subImportDeletePath  = "importDelete"
	subImportSuccessPath = "importSuccess"
)

// 定义允许的文件名关键字集合
var (
	allKeywords = []string{"章节练习", "章节真题", "历年真题", "模拟试卷"}

	// 匹配序号后、第一个下划线前的内容
	typePattern = regexp.MustCompile(`^\d+[.\s]*([^_]+)`)
	// 匹配 2020-2025 年的年份
	yearPattern = regexp.MustCompile(`202[0-5](年)?`)
)

// 检查文件名是否有效
func isValidFileName(fileName string) bool {
	typeMatcher := typePattern.FindStringSubmatch(fileName)
	if len(typeMatcher) <= 1 {
		return false
	}

	matchedType := typeMatcher[1]
	// 使用 slices.Contains 替代循环查找
	if !slices.Contains(allKeywords, matchedType) {
		return false
	}

	// 如果是历年真题,还需要检查年份
	if matchedType == "历年真题" {
		matchYear := yearPattern.MatchString(fileName)
		return matchYear
	}
	return true
}

func TestIsValidFileName() {
	fileName := "049.历年真题_历年真题(推荐~new)-2024二级建造师《公路实务》真题题A卷（网络版）.xlsx"
	if got := isValidFileName(fileName); !got {
	}
}

func QuestionsSetImport(baseDir string, subjectConfigs []types.SubjectConfig) {
	log.Println("开始导入题目...")

	for _, subjectConfig := range subjectConfigs {
		processConfig(baseDir, subjectConfig)
	}

	log.Println("导入完成")
}

func processConfig(baseDir string, subjectConfig types.SubjectConfig) {
	if subjectConfig.SubjectCode == "" {
		//log.Printf("跳过空科目代码的配置")
		return
	}

	// 检查证书是否在允许列表中
	allowedCertificates := []string{"一级建造师", "一级造价师"}
	certificateFound := false
	for _, cert := range allowedCertificates {
		if subjectConfig.JKDCertificateName == cert {
			certificateFound = true
			break
		}
	}
	// 只有当证书和科目都在允许列表中才继续执行
	if !certificateFound {
		log.Printf("跳过证书[%s]科目[%s]的导入", subjectConfig.JKDCertificateName, subjectConfig.SubjectCode)
		return
	}

	// 检查科目是否在允许列表中
	allowedSubjects := []string{
		"YJ-JSGCJJ",
	}
	subjectFound := false
	for _, subject := range allowedSubjects {
		if subjectConfig.SubjectCode == subject { // 假设使用 SubjectCode 作为科目代码字段
			subjectFound = true
			break
		}
	}
	if !subjectFound {
		log.Printf("跳过证书[%s]科目[%s]的导入", subjectConfig.JKDCertificateName, subjectConfig.SubjectCode)
		return
	}

	processSubjectDirectory(baseDir, subjectConfig)
}

func processSubjectDirectory(baseDir string, subjectConfig types.SubjectConfig) {
	dirPath := filepath.Join(baseDir, importFilePath, subjectConfig.JKDCertificateName)
	//dirPath := filepath.Join(baseDir, importFilePath, subjectConfig.CertificateCode)

	subjectDirs := findSubjectDir(dirPath, subjectConfig.JKDSubjectName)
	//subjectDirs := findSubjectDir(dirPath, subjectConfig.SubjectCode)

	if len(subjectDirs) == 0 {
		log.Printf("错误: 未找到科目目录")
		return
	}

	for _, subjectDir := range subjectDirs {
		processExcelFilesInDirectory(baseDir, subjectDir, subjectConfig)
	}
}

func processExcelFilesInDirectory(baseDir string, dirPath string, subjectConfig types.SubjectConfig) {
	var processedFiles int

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.Printf("错误: 访问路径失败: %v", err)
			return fmt.Errorf("访问路径失败: %w", err)
		}

		if !info.IsDir() && strings.HasSuffix(strings.ToLower(info.Name()), ".xlsx") {
			fileName := info.Name()

			// 检查文件名是否包含允许的关键字
			if !isValidFileName(fileName) {
				deleteDir := filepath.Join(baseDir, subImportDeletePath, subjectConfig.CertificateCode, subjectConfig.SubjectCode)
				if err := utils.CreateDirectory(deleteDir); err != nil {
					log.Printf("错误: 创建删除目录失败: %v", err)
					return fmt.Errorf("创建删除目录失败: %w", err)
				}
				utils.MoveToTargetDir(path, deleteDir)
				return nil
			}

			processedFiles++
			uploadFile(baseDir, path, subjectConfig)
		}
		return nil
	})

	if err != nil {
		log.Printf("错误: 遍历目录失败: %v", err)
		return
	}

	if processedFiles == 0 {
		log.Printf("错误: 目录中未找到 Excel 文件")
		return
	}
}

func uploadFile(baseDir string, filePath string, subjectConfig types.SubjectConfig) {
	subjectCode := subjectConfig.SubjectCode
	token := utils.MustGetEnv("AI_EXAM_TOKEN")
	fileName := filepath.Base(filePath)

	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	requestVO := fmt.Sprintf(`{"subjectCode":"%s"}`, subjectCode)
	if err := writer.WriteField("requestVO", requestVO); err != nil {
		log.Printf("错误: 写入 requestVO 失败: %v", err)
		return
	}

	file, err := os.Open(filePath)
	if err != nil {
		log.Printf("错误: 打开文件[%s]失败: %v", filePath, err)
		return
	}
	defer func() {
		if err := file.Close(); err != nil {
			log.Printf("警告: 关闭文件失败: %v", err)
		}
	}()

	part, err := writer.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		log.Printf("错误: 创建表单文件失败: %v", err)
		return
	}

	if _, err = io.Copy(part, file); err != nil {
		log.Printf("错误: 复制文件内容失败: %v", err)
		return
	}

	if err = writer.Close(); err != nil {
		log.Printf("错误: 关闭 writer 失败: %v", err)
		return
	}

	req, err := http.NewRequest("POST", baseURL, &requestBody)
	if err != nil {
		log.Printf("错误: 创建请求失败: %v", err)
		return
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("AI-Exam-Mgt-Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("错误: 发送请求失败: %v", err)
		return
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			log.Printf("警告: 关闭响应体失败: %v", err)
		}
	}()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("错误: 读取响应失败: %v", err)
		return
	}

	if resp.StatusCode == http.StatusOK {
		log.Printf("题库导入成功, 状态码: %d", resp.StatusCode)
		successImportDir := filepath.Join(baseDir, subImportSuccessPath, subjectConfig.CertificateCode, subjectConfig.SubjectCode)
		if err := utils.CreateDirectory(successImportDir); err != nil {
			log.Printf("错误: 创建导入成功目录失败: %v", err)
			return
		}
		utils.MoveToTargetDir(filePath, successImportDir)
		return
	}

	if resp.StatusCode != http.StatusOK {
		log.Printf("%s %s %s 导入错误: 请求失败, 状态码: %d, 响应: %s",
			filePath, fileName, subjectCode, resp.StatusCode, string(body))
		return
	}
}

func findSubjectDir(rootPath string, subjectName string) []string {
	if !filepath.IsAbs(rootPath) {
		absPath, err := filepath.Abs(rootPath)
		if err != nil {
			log.Printf("错误: 转换绝对路径失败: %v", err)
			return nil
		}
		rootPath = absPath
	}

	entries, err := os.ReadDir(rootPath)
	if err != nil {
		log.Printf("错误: 读取目录[%s]失败: %v", rootPath, err)
		return nil
	}

	var dirs []string
	for _, entry := range entries {
		if entry.IsDir() {
			name := entry.Name()
			parts := strings.Split(name, ".")
			if len(parts) < 2 {
				continue
			}
			cleanName := strings.TrimSpace(parts[1])
			if cleanName == subjectName {
				fullPath := filepath.Join(rootPath, name)
				dirs = append(dirs, fullPath)
			}
		}
	}

	//var dirs []string
	//for _, entry := range entries {
	//	if entry.IsDir() {
	//		name := entry.Name()
	//		if name == subjectName {
	//			fullPath := filepath.Join(rootPath, name)
	//			dirs = append(dirs, fullPath)
	//		}
	//	}
	//}

	return dirs
}
